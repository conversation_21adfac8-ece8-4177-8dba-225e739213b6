@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\dev\flockin\frontend\node_modules\.pnpm\@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3\node_modules\@angular\cli\bin\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3\node_modules\@angular\cli\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3\node_modules\@angular\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\dev\flockin\frontend\node_modules\.pnpm\@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3\node_modules\@angular\cli\bin\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3\node_modules\@angular\cli\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3\node_modules\@angular\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\@angular\cli\bin\ng.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\@angular\cli\bin\ng.js" %*
)
