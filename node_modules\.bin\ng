#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3/node_modules/@angular/cli/bin/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3/node_modules/@angular/cli/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3/node_modules/@angular/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3/node_modules/@angular/cli/bin/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3/node_modules/@angular/cli/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3/node_modules/@angular/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+cli@20.1.0_@types+node@24.0.14_chokidar@4.0.3/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@angular/cli/bin/ng.js" "$@"
else
  exec node  "$basedir/../@angular/cli/bin/ng.js" "$@"
fi
