"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.dev/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ResultKind = void 0;
var ResultKind;
(function (ResultKind) {
    ResultKind[ResultKind["Failure"] = 0] = "Failure";
    ResultKind[ResultKind["Full"] = 1] = "Full";
    ResultKind[ResultKind["Incremental"] = 2] = "Incremental";
    ResultKind[ResultKind["ComponentUpdate"] = 3] = "ComponentUpdate";
})(ResultKind || (exports.ResultKind = ResultKind = {}));
