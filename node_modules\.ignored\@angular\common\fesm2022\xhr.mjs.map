{"version": 3, "file": "xhr.mjs", "sources": ["../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/cookie.ts", "../../../../../k8-fastbuild-ST-46c76129e412/bin/packages/common/src/xhr.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\nexport function parseCookieValue(cookieStr: string, name: string): string | null {\n  name = encodeURIComponent(name);\n  for (const cookie of cookieStr.split(';')) {\n    const eqIndex = cookie.indexOf('=');\n    const [cookieName, cookieValue]: string[] =\n      eqIndex == -1 ? [cookie, ''] : [cookie.slice(0, eqIndex), cookie.slice(eqIndex + 1)];\n    if (cookieName.trim() === name) {\n      return decodeURIComponent(cookieValue);\n    }\n  }\n  return null;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n/**\n * A wrapper around the `XMLHttpRequest` constructor.\n *\n * @publicApi\n */\nexport abstract class XhrFactory {\n  abstract build(): XMLHttpRequest;\n}\n"], "names": [], "mappings": ";;;;;;AAQgB,SAAA,gBAAgB,CAAC,SAAiB,EAAE,IAAY,EAAA;AAC9D,IAAA,IAAI,GAAG,kBAAkB,CAAC,IAAI,CAAC;IAC/B,KAAK,MAAM,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;QACzC,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC;AACnC,QAAA,MAAM,CAAC,UAAU,EAAE,WAAW,CAAC,GAC7B,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;AACtF,QAAA,IAAI,UAAU,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE;AAC9B,YAAA,OAAO,kBAAkB,CAAC,WAAW,CAAC;;;AAG1C,IAAA,OAAO,IAAI;AACb;;ACXA;;;;AAIG;MACmB,UAAU,CAAA;AAE/B;;;;"}