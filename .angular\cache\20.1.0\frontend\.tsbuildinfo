{"fileNames": ["../../../../src/main.ngtypecheck.ts", "../../../../src/app/app.config.ngtypecheck.ts", "../../../../src/app/app.routes.ngtypecheck.ts", "../../../../src/app/core/guards/auth.guard.ngtypecheck.ts", "../../../../src/app/core/services/auth.service.ngtypecheck.ts", "../../../../src/environments/environment.ngtypecheck.ts", "../../../../src/environments/environment.ts", "../../../../src/app/core/services/auth.service.ts", "../../../../src/app/core/guards/auth.guard.ts", "../../../../src/app/features/landing/landing.component.ngtypecheck.ts", "../../../../src/app/features/landing/landing.component.ts", "../../../../src/app/features/auth/components/login/login.component.ngtypecheck.ts", "../../../../src/app/features/auth/components/login/login.component.ts", "../../../../src/app/features/auth/components/signup/signup.component.ngtypecheck.ts", "../../../../src/app/core/services/church.service.ngtypecheck.ts", "../../../../src/app/core/services/church.service.ts", "../../../../src/app/features/auth/components/signup/signup.component.ts", "../../../../src/app/features/auth/components/oauth-callback/oauth-callback.component.ngtypecheck.ts", "../../../../src/app/features/auth/components/oauth-callback/oauth-callback.component.ts", "../../../../src/app/layout/sidebar/sidebar.component.ngtypecheck.ts", "../../../../src/app/layout/sidebar/sidebar.component.ts", "../../../../src/app/layout/header/header.component.ngtypecheck.ts", "../../../../src/app/layout/header/header.component.ts", "../../../../src/app/layout/dashboard-layout/dashboard-layout.component.ngtypecheck.ts", "../../../../src/app/layout/dashboard-layout/dashboard-layout.component.ts", "../../../../src/app/features/dashboard/dashboard.component.ngtypecheck.ts", "../../../../src/app/core/services/attendance.service.ngtypecheck.ts", "../../../../src/app/core/services/attendance.service.ts", "../../../../src/app/core/services/members.service.ngtypecheck.ts", "../../../../src/app/core/services/members.service.ts", "../../../../src/app/features/dashboard/dashboard.component.ts", "../../../../src/app/features/attendance/live-attendance/live-attendance.component.ngtypecheck.ts", "../../../../src/app/features/attendance/live-attendance/live-attendance.component.ts", "../../../../src/app/shared/components/attendance-chart/attendance-chart.component.ngtypecheck.ts", "../../../../src/app/shared/components/attendance-chart/attendance-chart.component.ts", "../../../../src/app/features/reports/reports.component.ngtypecheck.ts", "../../../../../node_modules/.pnpm/jspdf@3.0.1/node_modules/jspdf/types/index.d.ts", "../../../../../node_modules/.pnpm/jspdf-autotable@5.0.2_jspdf@3.0.1/node_modules/jspdf-autotable/dist/index.d.ts", "../../../../src/app/features/reports/reports.component.ts", "../../../../src/app/features/members/members.component.ngtypecheck.ts", "../../../../src/app/features/members/add-member-dialog/add-member-dialog.component.ngtypecheck.ts", "../../../../src/app/features/members/add-member-dialog/add-member-dialog.component.ts", "../../../../src/app/features/members/members.component.ts", "../../../../src/app/features/settings/settings.component.ngtypecheck.ts", "../../../../src/app/core/services/notifications.service.ngtypecheck.ts", "../../../../src/app/core/services/notifications.service.ts", "../../../../src/app/features/settings/settings.component.ts", "../../../../src/app/features/notifications/notifications.component.ngtypecheck.ts", "../../../../src/app/features/notifications/notifications.component.ts", "../../../../src/app/app.routes.ts", "../../../../src/app/core/interceptors/auth.interceptor.ngtypecheck.ts", "../../../../src/app/core/interceptors/auth.interceptor.ts", "../../../../src/app/app.config.ts", "../../../../src/app/app.ngtypecheck.ts", "../../../../src/app/app.ts", "../../../../src/main.ts", "../../../../src/app/shared/components/shared-components.module.ngtypecheck.ts", "../../../../src/app/shared/components/location-picker/location-picker.component.ngtypecheck.ts", "../../../../../node_modules/.pnpm/@types+geojson@7946.0.16/node_modules/@types/geojson/index.d.ts", "../../../../../node_modules/.pnpm/@types+leaflet@1.9.20/node_modules/@types/leaflet/index.d.ts", "../../../../src/app/shared/components/location-picker/location-picker.component.ts", "../../../../src/app/shared/components/logo-upload/logo-upload.component.ngtypecheck.ts", "../../../../src/app/shared/components/logo-upload/logo-upload.component.ts", "../../../../src/app/shared/components/shared-components.module.ts", "../../../../src/environments/environment.prod.ngtypecheck.ts", "../../../../src/environments/environment.prod.ts"], "fileIdsList": [[2, 50, 52], [55], [3, 9, 11, 13, 17, 19, 25, 31, 33, 39, 43, 47, 49], [54], [4, 7, 8], [8, 51], [7, 27], [5, 7], [7, 15], [7, 29], [7, 45], [33], [8, 28, 30, 32], [13], [8, 12], [19], [8, 18], [17], [8, 14, 16], [31], [8, 26, 28, 30], [11], [7, 10], [42], [8, 30, 41], [43], [8, 30, 40, 42], [49], [46, 48], [35, 39], [8, 16, 28, 35, 36, 37, 38], [47], [8, 16, 44, 46], [21, 23, 25], [8, 21, 23, 24], [23], [8, 22], [21], [8, 20], [35], [34], [61], [58, 60], [63], [62], [57, 61, 63], [65], [6], [1, 53, 55], [59]], "fileInfos": [{"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "4bde1d4f7b399ffea09b2c61b2b730bd149a49b030cbade757e611bf4be57ac3", "signature": "1a6db0ae42a0bddd62739659905a9ff38def5e6cbd31e6e1a246e8681f702c62"}, {"version": "ae828936ade6a0ad507e13199bcf52988e45a3f390b8f5c5abfa6d41faedce0b", "signature": "9fd4cb5164f05bd81c992434568b4423248cd174b97e73f80ad911438148c78a"}, {"version": "ccf45f0dbcda39dccc48f3c45ffd608f810165f2dbbf6c6c98d45663556c44cf", "signature": "9992e682705f419ffef067888a1ae580a9694907c9c1079af6905ae78f319858"}, {"version": "eee0e4918b4ddc92c96d4a9f7bbd0a7213ebd1ddf8ac93ba0b40bb0a7c83e37a", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "85a4002e1f7097179d5c355e5e3cad78f42e378ac27e09fded605d7542a3ec7f", "signature": "dc9154ac79fccd6b05e03abbfcbe2c6bb4b5271c7778018ad0281228ef642437"}, {"version": "bc305226bd4e6213bd7eb7560dc6351f7a912438fe4cce20ef766b2461d70f51", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "f1cd8aaa9d3c0eb33803396d2ac5a3b5fd97300450268acd8882cf7a7b64e273", "signature": "239130cea73bd276ee04697c475dc56715451332f49935fffaa7ef4d37413519"}, {"version": "e763e246b897b60d869325f7509b6bd60aa67db5b7d6d6ecace9a887321de570", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "27ece0429a7bd73b40ab79d3b0131e9aade056340b3a265ee12761bf19dcde59", "signature": "c8e955252caba550c77ad0bb418aa08bbc223517efa77085c9d9fd9be47109f4"}, {"version": "773464eaffddbd57a8e15d490fa0523f83749131e8d3a2fc1d985e46de6b6b4a", "signature": "77376be18fea0a0a853e9b6250f6f5e34923d59db32b0546cfb0b7094b2dd97e"}, {"version": "2723dbb4f455b0f6cb3d45faa3d17f301a9d5bda477b1e9ee42ab88b1b84975c", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "477e15ad226db49e98d58c88ea35f34d3968139dd4a40ed939dc083298334947", "signature": "6ac425e3a8808293332640eb0f3d18a467eabae9a42d74568bedc7998b2b30f0"}, {"version": "9784563419957250c4b64f656a90f5737d4712f1d27625ccfd13878f405e0c88", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "4420dc8db76cf05c9a697ecbff9325563facb9cae5d542ec5f1c93748de71c05", "signature": "7bd8d417a71b8751e3aebdb646ad326e49ed4a2c680f692d34481542e0c8d937"}, {"version": "1a4366ee68a49fb3c9b2cc3da85d5f491e08c93ade680a062d3e6e3f6dad8be4", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "3f653ab22dd4c6548a9f034a14a871a7c61106097468718228ee54578602df40", "signature": "31b0c384093ffb0f2a4e6b16846012449ba2b55074b4f783c5dfe8ea8c7130b2"}, {"version": "342b1813490aba55837f87a47e25034300f6ea60692a2fa50e5b2f5e88fca0d4", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "d957656eb1478d3745767ef5db50489a8b8547dcefecd207f2d9c3b802ade580", "signature": "458f2bfd7b9a8a9d33800b08ef0a4f9c95e39c845ed369dbcc1ae847c863dbd7"}, {"version": "5c10d8190c6d6980954f1b7d87cb41f1c240f6a341eb102192f2f557b54d203c", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "52a86e754f778b19dfffb6dd085231ca26ce834af19356eb8a435b81d3d2ea63", "signature": "565e5f780488e828f501380465020dff9d0e8e45ef49b6d49737b154c28f8a95"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "2d9feb521e7cd6b888d0bfffe4929a594865dd975b18943d7cb1b8184b54c62c", "signature": "a3e400fb917144619d1f32e06cd1356c3d026270db9aff62fd068b446e17cf81"}, {"version": "14d9783c382bb6a24c3a84deaf7f0aa798e783138bd4d08b2177ddd20dd592ad", "signature": "94fbd99a9813d3f6015ec0ce38763959ec0e07b42bea7e80c50c78f4868cb795"}, {"version": "37166d8eba68a17e2448fa19b270acc7ee645ad179192d36175a244136e6aee8", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "4639ec11eb0057f2c1d92dc4cd6f2053f594332ba0be5f35546942b25d081cdc", "signature": "fbed937210f27f5e0c40978bc80dbf417293cfd2168b6a7a551eeefb3d2e11af"}, {"version": "032c23dd9098434680f8c74b2db4d43a77b77a945d98f4f5b6866bce7c6e108e", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "588865c4b5fa807902538311dea0b2ed7ab547e2298a778419e74a361fe36ec2", "signature": "4511ae141c5a7bf86ed573ce65262449a0424abe94f23eb97e1e88c0e0b138a3"}, {"version": "dc9344d4a4f0f7195addd2da0fd24c919d2fa8e36965c95bbb35c58f1a02a20a", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "caef5b191982cd88619282b10e1c52c3cde8c81d4eaf4650b4e62d73f77483d4", "impliedFormat": 1}, {"version": "fb67e9d5d8ec38dd6465a77ebc0b5e2c31fbb787e709cea1ba9e3079cdf9f398", "impliedFormat": 1}, {"version": "dae729ecff3e7dd46ce9b16fee82ec16220308e5a50989ffddbccb355970dde5", "signature": "e08609ec058f56f0eb5347bfe85f98d83988ecf2e10423a8ecd73208447fc310"}, {"version": "e258be6e0fa4c2d44ecd927056f1bc54cebc1e9231c11be8016bf3c24c87f72c", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "ed69003a705dae0b10916424cb646a753ce13a0ef0d8025951391d1d3891f07a", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "54ed80a3b1473e0cc9403fb5b019eca707fe1bd94733b8399db801411998894d", "signature": "3d1e435cd9211d8dc646f612f6ee5eae205511061e9739889e306b88035eec36"}, {"version": "2ce780537ba5af8bd0db1e631270ddbb12813b7bfb31a43bdc3250002d96fc1b", "signature": "ec83d0c994a2ff49cf63805b5ac5a8b17731c255d96fc0fb2707168c0dce95ff"}, {"version": "19bde549e20979941908e11d707251e40877857ce5c5b6a6aa191233010377b9", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "71bcb90cd3e1eaa9840f372f1b63f928a89b257097be7bb2e68753bb5b185ff9", "signature": "2b3ae3f54d24971fc5cbf761811aadfd7d9946e4fa68c65c3c0aec2dbd773de2"}, {"version": "ad37b36f21a16461e990451be86c2087eed9930832900d813398ef0add79117c", "signature": "9053b51e7bd8c76cd60331783bb18222792cbd91081199e42c902323f23f562e"}, {"version": "04e1df669e63d833a526c32cecdc86daa25460b83079ec110df7c1482f89820f", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "f75f06f4454b1861df86ded452b90f4bcb37dea89dd28efd0de4678231c02861", "signature": "fc2cb615a028a45cef684d6082b5c431fe588c431cb02da26d1c3c1bfc105fb9"}, {"version": "8496faa299ba4eba9495734df253633c80ff4c55acbe99c30c8dcb3aacf18a96", "signature": "fe23f4b686e191a57f63331b1016a7c543e2f86cd96e678d9034ffa71f777ba3"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "0d14af3f1dd818c106e4c50432d126d98c7f67f6c4b37c6628a7a3d9fa91a635", "signature": "0f85a7c1fe33a91a589145067aca211a6c638c8f5423045e55aa4f1d39f223ed"}, {"version": "c1a84733e08bae46fe7651186eb55cfb167b84e01744af44261f45dec1eb543c", "signature": "55bb18d80ab4836734d6b6075750b673bec340bcaed10fed842407a1fdeb3657"}, {"version": "20543c9a87401cf8224feb917aaf229dba9790824d97f598c7d920b18ca8a40c", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "ef6ecfe00ef86f81813b54164b9b9c6dc74b0e94ca41a8524ec3907f3f0ffd8a", "signature": "df7323eccfa1fe67b16db79fd095901949b9105a2ac8a809d83a94f57759b3a5"}, {"version": "a53d8bf50034149a8d8b171377d40bfa2cb72535f30894af63a4429355332c76", "signature": "8e609bb71c20b858c77f0e9f90bb1319db8477b13f9f965f1a1e18524bf50881"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "710aeb10ec1b4643114b84f0004f313d385b83fbaf1dd2cdc2ca0e0c3a88fa0e", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "d30e67059f5c545c5f8f0cc328a36d2e03b8c4a091b4301bc1d6afb2b1491a3a", "impliedFormat": 1}, {"version": "210469f9985f125b0725d46b0c97a3cf367904463c138030f4c86217510316e9", "impliedFormat": 1}, {"version": "ea84454080e793e85e47dc594bc7f961354d8590acd3886747bc8e4ab02ba14e", "signature": "41cf52bb3e79dc0d066b2488eec22b1299eaa166fc1d3f0069d6f1dbbe2b6c7e"}, {"version": "170420c63c5d4c1d9829d545908715716dd0d72b00385dbce7b922fe047c2815", "signature": "b52dcd199c97746007e4589749483d8b943e6bac0bbf6a90c0b9c7be86f9b793"}, {"version": "89a8745f3a91ae61da649794410a57aa86b4ec2a0f7dc084247b47f5aa8280ae", "signature": "421176424341c1ee9b7b55e3f83b33853c39ea4253301ef0635fa5fbc3489923"}, {"version": "2a967f61b551275796a6689096c95e77057b21dc89fc61bc4453a1695d483b0d", "signature": "54fb6e4742d17d372adb264f50629ce1dcf4564dbf3ec7574cff2bda80e31bda"}, {"version": "ddd578018a259d1c494c834bdd8707769d07d1eb64f87f5217560cd2181b9e93", "signature": "da14a67372982ca6e605fea114900b492b3316618581634e0ce72afbcb09baca"}, {"version": "700979b6754e6297ae92b275f16860bcf457b650371829fc3eb7d0033d669b75", "signature": "54f3b148870c8b2f3e4b49a87258de086768e0b7e3c6b5a93c818e057a6c69df"}], "root": [[1, 36], [39, 58], [61, 66]], "options": {"composite": false, "declaration": false, "declarationMap": false, "experimentalDecorators": true, "importHelpers": true, "inlineSourceMap": true, "inlineSources": true, "module": 200, "noEmitOnError": false, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "noImplicitReturns": true, "noPropertyAccessFromIndexSignature": true, "outDir": "../../../..", "removeComments": false, "skipLibCheck": true, "strict": true, "target": 9, "tsBuildInfoFile": "./.tsbuildinfo"}, "referencedMap": [[53, 1], [54, 2], [50, 3], [55, 4], [9, 5], [52, 6], [28, 7], [8, 8], [16, 9], [30, 10], [46, 11], [32, 12], [33, 13], [12, 14], [13, 15], [18, 16], [19, 17], [14, 18], [17, 19], [26, 20], [31, 21], [10, 22], [11, 23], [41, 24], [42, 25], [40, 26], [43, 27], [48, 28], [49, 29], [36, 30], [39, 31], [44, 32], [47, 33], [24, 34], [25, 35], [22, 36], [23, 37], [20, 38], [21, 39], [34, 40], [35, 41], [58, 42], [61, 43], [62, 44], [63, 45], [64, 46], [66, 47], [7, 48], [56, 49], [60, 50]], "semanticDiagnosticsPerFile": [1, 2, 3, 4, 5, 6, [8, [{"start": 35, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 79, "length": 22, "messageText": "Cannot find module '@angular/common/http' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 126, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 178, "length": 20, "messageText": "Cannot find module '@auth0/angular-jwt' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 256, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 301, "length": 16, "messageText": "Cannot find module 'rxjs/operators' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 711, "length": 37, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 1777, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 2111, "length": 7, "messageText": "Cannot find name 'Promise'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2015' or later.", "category": 1, "code": 2583}, {"start": 2553, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 2607, "length": 5, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 2942, "length": 7, "messageText": "Cannot find name 'Promise'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2015' or later.", "category": 1, "code": 2583}, {"start": 3384, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3445, "length": 5, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 3636, "length": 6, "messageText": "Cannot find name 'window'.", "category": 1, "code": 2304}, {"start": 4194, "length": 5, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 4356, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4559, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4827, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5398, "length": 12, "messageText": "Cannot find name 'localStorage'.", "category": 1, "code": 2304}, {"start": 5524, "length": 12, "messageText": "Cannot find name 'localStorage'.", "category": 1, "code": 2304}, {"start": 5828, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 6560, "length": 12, "messageText": "Cannot find name 'localStorage'.", "category": 1, "code": 2304}, {"start": 6615, "length": 12, "messageText": "Cannot find name 'localStorage'.", "category": 1, "code": 2304}, {"start": 6728, "length": 12, "messageText": "Cannot find name 'localStorage'.", "category": 1, "code": 2304}, {"start": 6764, "length": 4, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 6852, "length": 12, "messageText": "Cannot find name 'localStorage'.", "category": 1, "code": 2304}, {"start": 6911, "length": 4, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 6981, "length": 12, "messageText": "Cannot find name 'localStorage'.", "category": 1, "code": 2304}, {"start": 7026, "length": 12, "messageText": "Cannot find name 'localStorage'.", "category": 1, "code": 2304}, {"start": 7118, "length": 12, "messageText": "Cannot find name 'localStorage'.", "category": 1, "code": 2304}]], [9, [{"start": 23, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 63, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 112, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 294, "length": 5, "messageText": "Parameter 'route' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 301, "length": 5, "messageText": "Parameter 'state' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 641, "length": 5, "messageText": "Parameter 'route' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 648, "length": 5, "messageText": "Parameter 'state' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 964, "length": 5, "messageText": "Parameter 'route' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 971, "length": 5, "messageText": "Parameter 'state' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 10, [11, [{"start": 26, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 72, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 120, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 171, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 229, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 288, "length": 27, "messageText": "Cannot find module '@angular/material/toolbar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 347, "length": 24, "messageText": "Cannot find module '@angular/material/card' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 403, "length": 24, "messageText": "Cannot find module '@angular/material/menu' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 472, "length": 26, "messageText": "Cannot find module '@angular/material/dialog' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 547, "length": 29, "messageText": "Cannot find module '@angular/material/snack-bar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 644, "length": 342, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 3110, "length": 8, "messageText": "Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3258, "length": 8, "messageText": "Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}]], 12, [13, [{"start": 34, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 80, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 171, "length": 16, "messageText": "Cannot find module '@angular/forms' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 242, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 332, "length": 200, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 991, "length": 7, "messageText": "Cannot find name 'Promise'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2015' or later.", "category": 1, "code": 2583}, {"start": 1797, "length": 6, "messageText": "Cannot find name 'Object'.", "category": 1, "code": 2304}, {"start": 1842, "length": 3, "messageText": "Parameter 'key' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 14, 15, [16, [{"start": 35, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 79, "length": 22, "messageText": "Cannot find module '@angular/common/http' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 147, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 192, "length": 16, "messageText": "Cannot find module 'rxjs/operators' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1393, "length": 37, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 2062, "length": 6, "messageText": "Parameter 'church' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2210, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2586, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 2673, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 2924, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2946, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3160, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3179, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3632, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3707, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3771, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 4022, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4044, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 4267, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4286, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 4358, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 4427, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 4498, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 5100, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5268, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5842, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'number'."}, {"start": 5875, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'number'."}, {"start": 5904, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'number'."}]], [17, [{"start": 34, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 80, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 188, "length": 16, "messageText": "Cannot find module '@angular/forms' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 243, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 293, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 454, "length": 206, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 1968, "length": 7, "messageText": "Cannot find name 'Promise'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2015' or later.", "category": 1, "code": 2583}, {"start": 2187, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 2400, "length": 7, "messageText": "Cannot find name 'Promise'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2015' or later.", "category": 1, "code": 2583}, {"start": 3303, "length": 6, "messageText": "Cannot find name 'Object'.", "category": 1, "code": 2304}, {"start": 3349, "length": 3, "messageText": "Parameter 'key' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 18, [19, [{"start": 42, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 88, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 146, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 242, "length": 192, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 807, "length": 6, "messageText": "Parameter 'params' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 921, "length": 18, "messageText": "Cannot find name 'decodeURIComponent'.", "category": 1, "code": 2304}, {"start": 1099, "length": 4, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 1110, "length": 18, "messageText": "Cannot find name 'decodeURIComponent'.", "category": 1, "code": 2304}]], 20, [21, [{"start": 55, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 101, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 157, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 209, "length": 27, "messageText": "Cannot find module '@angular/material/sidenav' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 268, "length": 24, "messageText": "Cannot find module '@angular/material/list' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 324, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 383, "length": 27, "messageText": "Cannot find module '@angular/material/divider' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 574, "length": 279, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 1912, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'filter' does not exist on type '{}'."}, {"start": 1919, "length": 4, "messageText": "Parameter 'item' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 22, [23, [{"start": 63, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 109, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 161, "length": 27, "messageText": "Cannot find module '@angular/material/toolbar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 222, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 280, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 336, "length": 24, "messageText": "Cannot find module '@angular/material/menu' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 393, "length": 25, "messageText": "Cannot find module '@angular/material/badge' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 453, "length": 27, "messageText": "Cannot find module '@angular/material/divider' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 540, "length": 299, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 1263, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'charAt' does not exist on type 'string'."}, {"start": 1289, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'charAt' does not exist on type 'string'."}, {"start": 1301, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'toUpperCase' does not exist on type 'string'."}]], 24, [25, [{"start": 34, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 80, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 128, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 339, "length": 247, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 817, "length": 9, "messageText": "Parameter 'collapsed' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 912, "length": 4, "messageText": "Parameter 'open' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 26, 27, [28, [{"start": 35, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 79, "length": 22, "messageText": "Cannot find module '@angular/common/http' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 147, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 180, "length": 16, "messageText": "Cannot find module 'rxjs/operators' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 3478, "length": 37, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 5130, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 5146, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 5725, "length": 4, "messageText": "Parameter 'data' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6166, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'number'."}, {"start": 6203, "length": 4, "messageText": "Parameter 'data' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6451, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 6467, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 6876, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 6892, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 7560, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'number'."}, {"start": 7730, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 7750, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}]], 29, [30, [{"start": 35, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 79, "length": 22, "messageText": "Cannot find module '@angular/common/http' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 147, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 175, "length": 16, "messageText": "Cannot find module 'rxjs/operators' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1842, "length": 37, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 3612, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'boolean'."}, {"start": 3649, "length": 4, "messageText": "Parameter 'data' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4056, "length": 4, "messageText": "Parameter 'data' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4348, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'trim' does not exist on type 'string'."}, {"start": 4397, "length": 8, "messageText": "Parameter 'observer' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4558, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'trim' does not exist on type 'string'."}, {"start": 4837, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'number'."}]], [31, [{"start": 63, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 109, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 151, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 220, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 258, "length": 24, "messageText": "Cannot find module '@angular/material/card' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 316, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 374, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 441, "length": 36, "messageText": "Cannot find module '@angular/material/progress-spinner' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 512, "length": 27, "messageText": "Cannot find module '@angular/material/divider' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 572, "length": 25, "messageText": "Cannot find module '@angular/material/chips' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 857, "length": 316, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 2497, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 2559, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 2616, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3082, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3165, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3366, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3583, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 4039, "length": 46, "messageText": "Type '[any, any, any]' must have a '[Symbol.iterator]()' method that returns an iterator.", "category": 1, "code": 2488}, {"start": 4040, "length": 15, "messageText": "Binding element 'attendanceStats' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 4057, "length": 14, "messageText": "Binding element 'liveAttendance' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 4073, "length": 11, "messageText": "Binding element 'memberStats' implicitly has an 'any' type.", "category": 1, "code": 7031}, {"start": 4100, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 4489, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4509, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 4575, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 5685, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 6058, "length": 14, "code": 2339, "category": 1, "messageText": "Property 'toLocaleString' does not exist on type 'number'."}, {"start": 6139, "length": 4, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}]], 32, [33, [{"start": 63, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 124, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 171, "length": 16, "messageText": "Cannot find module '@angular/forms' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 212, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 313, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1066, "length": 223, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 1790, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 1800, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 2318, "length": 6, "messageText": "Parameter 'record' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2473, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2535, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 2583, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 2617, "length": 4, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}, {"start": 3254, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3328, "length": 1, "messageText": "Parameter 'r' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3462, "length": 4, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}, {"start": 4361, "length": 11, "messageText": "Cannot find name 'set<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 4527, "length": 13, "messageText": "Cannot find name 'clearI<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 4724, "length": 13, "messageText": "Cannot find name 'clearI<PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 5105, "length": 7, "messageText": "Cannot find name 'Promise'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2015' or later.", "category": 1, "code": 2583}, {"start": 5330, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5347, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 5774, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'filter' does not exist on type '{}'."}, {"start": 5781, "length": 6, "messageText": "Parameter 'record' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6123, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'filter' does not exist on type '{}'."}, {"start": 6130, "length": 6, "messageText": "Parameter 'record' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6283, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'sort' does not exist on type '{}'."}, {"start": 6289, "length": 1, "messageText": "Parameter 'a' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6292, "length": 1, "messageText": "Parameter 'b' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6569, "length": 13, "code": 2339, "category": 1, "messageText": "Property 'localeCompare' does not exist on type 'string'."}, {"start": 6663, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 6699, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 7559, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 7679, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 8395, "length": 25, "messageText": "Type '[LiveAttendance, {}]' must have a '[Symbol.iterator]()' method that returns an iterator.", "category": 1, "code": 2488}, {"start": 8572, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 8659, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 8897, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 8929, "length": 4, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}, {"start": 9073, "length": 4, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}, {"start": 10321, "length": 4, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}, {"start": 10368, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'toFixed' does not exist on type 'number'."}]], 34, [35, [{"start": 90, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 136, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 223, "length": 10, "messageText": "Cannot find module 'chart.js' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1067, "length": 2608, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 4039, "length": 17, "messageText": "Cannot find name 'HTMLCanvasElement'.", "category": 1, "code": 2304}, {"start": 5305, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'map' does not exist on type '{}'."}, {"start": 5309, "length": 7, "messageText": "Parameter 'dataset' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5908, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'map' does not exist on type '{}'."}, {"start": 5912, "length": 7, "messageText": "Parameter 'dataset' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 36, [39, [{"start": 63, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 109, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 156, "length": 16, "messageText": "Cannot find module '@angular/forms' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 209, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 249, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 307, "length": 24, "messageText": "Cannot find module '@angular/material/menu' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 363, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1003, "length": 257, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 3829, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'filter' does not exist on type '{}'."}, {"start": 3836, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4071, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'filter' does not exist on type '{}'."}, {"start": 4078, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4653, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'reduce' does not exist on type '{}'."}, {"start": 4661, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4666, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4753, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 4789, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'reduce' does not exist on type '{}'."}, {"start": 4797, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4802, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4857, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 5206, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'filter' does not exist on type '{}'."}, {"start": 5213, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5450, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'filter' does not exist on type '{}'."}, {"start": 5457, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5958, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'filter' does not exist on type '{}'."}, {"start": 5965, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6154, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'reduce' does not exist on type '{}'."}, {"start": 6162, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6167, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6257, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 6293, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'reduce' does not exist on type '{}'."}, {"start": 6301, "length": 3, "messageText": "Parameter 'sum' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6306, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6363, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 6950, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6985, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 7205, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7482, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7873, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7966, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 7971, "length": 8, "messageText": "Cannot find name 'parseInt'.", "category": 1, "code": 2304}, {"start": 7987, "length": 8, "messageText": "Cannot find name 'parseInt'.", "category": 1, "code": 2304}, {"start": 8212, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8492, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 9078, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 9138, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'map' does not exist on type '{}'."}, {"start": 9225, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 9436, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 9441, "length": 8, "messageText": "Cannot find name 'parseInt'.", "category": 1, "code": 2304}, {"start": 9457, "length": 8, "messageText": "Cannot find name 'parseInt'.", "category": 1, "code": 2304}, {"start": 9681, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'map' does not exist on type '{}'."}, {"start": 10204, "length": 4, "messageText": "Parameter 'user' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10436, "length": 6, "messageText": "Parameter 'church' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10524, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10548, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 12402, "length": 10, "messageText": "Parameter 'exportData' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 12491, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 12511, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 13303, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 13860, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 14471, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 14727, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 14743, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 14820, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 14926, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 15012, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 15091, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 15128, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 15969, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 16025, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 16070, "length": 4, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}, {"start": 16298, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 16399, "length": 4, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 16708, "length": 4, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 16947, "length": 4, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 17117, "length": 4, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 17123, "length": 4, "messageText": "Cannot find name '<PERSON><PERSON><PERSON>'.", "category": 1, "code": 2304}, {"start": 17287, "length": 6, "messageText": "Cannot find name 'window'.", "category": 1, "code": 2304}, {"start": 17338, "length": 8, "messageText": "Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 17422, "length": 8, "messageText": "Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 17477, "length": 8, "messageText": "Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 17514, "length": 6, "messageText": "Cannot find name 'window'.", "category": 1, "code": 2304}, {"start": 18589, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 19454, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 20221, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 21281, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'charAt' does not exist on type 'string'."}, {"start": 21318, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'slice' does not exist on type 'string'."}, {"start": 22569, "length": 7, "messageText": "Parameter 'service' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 23364, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'charAt' does not exist on type 'string'."}, {"start": 23401, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'slice' does not exist on type 'string'."}, {"start": 23487, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 28720, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 30025, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 30094, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 31481, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 32924, "length": 4, "messageText": "Parameter 'week' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 32971, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 34065, "length": 5, "messageText": "Parameter 'month' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 40, 41, [42, [{"start": 42, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 88, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 179, "length": 16, "messageText": "Cannot find module '@angular/forms' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 260, "length": 26, "messageText": "Cannot find module '@angular/material/dialog' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 323, "length": 30, "messageText": "Cannot find module '@angular/material/form-field' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 386, "length": 25, "messageText": "Cannot find module '@angular/material/input' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 445, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 505, "length": 26, "messageText": "Cannot find module '@angular/material/select' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 569, "length": 30, "messageText": "Cannot find module '@angular/material/datepicker' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 637, "length": 24, "messageText": "Cannot find module '@angular/material/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 693, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 760, "length": 36, "messageText": "Cannot find module '@angular/material/progress-spinner' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 826, "length": 29, "messageText": "Cannot find module '@angular/material/snack-bar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1082, "length": 440, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 4278, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4563, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [43, [{"start": 52, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 98, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 145, "length": 16, "messageText": "Cannot find module '@angular/forms' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 193, "length": 24, "messageText": "Cannot find module '@angular/material/card' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 251, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 309, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 366, "length": 25, "messageText": "Cannot find module '@angular/material/input' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 428, "length": 30, "messageText": "Cannot find module '@angular/material/form-field' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 491, "length": 25, "messageText": "Cannot find module '@angular/material/table' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 553, "length": 29, "messageText": "Cannot find module '@angular/material/paginator' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 614, "length": 24, "messageText": "Cannot find module '@angular/material/sort' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 671, "length": 25, "messageText": "Cannot find module '@angular/material/chips' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 739, "length": 36, "messageText": "Cannot find module '@angular/material/progress-spinner' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 807, "length": 24, "messageText": "Cannot find module '@angular/material/menu' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 865, "length": 26, "messageText": "Cannot find module '@angular/material/dialog' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 927, "length": 29, "messageText": "Cannot find module '@angular/material/snack-bar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 990, "length": 26, "messageText": "Cannot find module '@angular/material/select' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1052, "length": 28, "messageText": "Cannot find module '@angular/material/checkbox' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1115, "length": 27, "messageText": "Cannot find module '@angular/material/tooltip' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1177, "length": 27, "messageText": "Cannot find module '@angular/material/divider' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1480, "length": 26, "messageText": "Cannot find module '@angular/material/dialog' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1536, "length": 29, "messageText": "Cannot find module '@angular/material/snack-bar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1568, "length": 563, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 2629, "length": 6, "messageText": "Parameter 'member' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3483, "length": 7, "messageText": "Parameter 'members' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3598, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3620, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 3964, "length": 5, "messageText": "Parameter 'stats' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4042, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4064, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 4475, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'toUpperCase' does not exist on type 'string'."}, {"start": 4712, "length": 11, "code": 2339, "category": 1, "messageText": "Property 'toUpperCase' does not exist on type 'string'."}, {"start": 4974, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 5103, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 5230, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 5374, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 5938, "length": 6, "messageText": "Parameter 'result' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 44, 45, [46, [{"start": 35, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 79, "length": 22, "messageText": "Cannot find module '@angular/common/http' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 147, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 187, "length": 16, "messageText": "Cannot find module 'rxjs/operators' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 420, "length": 6, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}, {"start": 484, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 603, "length": 6, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}, {"start": 1049, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 1066, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 1752, "length": 37, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 2689, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3402, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 3894, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'number'."}, {"start": 3921, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'toString' does not exist on type 'number'."}, {"start": 3958, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4098, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4578, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4680, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4700, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 5017, "length": 3, "code": 2339, "category": 1, "messageText": "Property 'map' does not exist on type '{}'."}, {"start": 5021, "length": 2, "messageText": "Parameter 'st' implicitly has an 'any' type.", "category": 1, "code": 7006}]], [47, [{"start": 53, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 99, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 201, "length": 16, "messageText": "Cannot find module '@angular/forms' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 249, "length": 24, "messageText": "Cannot find module '@angular/material/tabs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 305, "length": 24, "messageText": "Cannot find module '@angular/material/card' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 363, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 421, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 478, "length": 25, "messageText": "Cannot find module '@angular/material/input' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 540, "length": 30, "messageText": "Cannot find module '@angular/material/form-field' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 604, "length": 26, "messageText": "Cannot find module '@angular/material/select' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 664, "length": 26, "messageText": "Cannot find module '@angular/material/slider' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 733, "length": 36, "messageText": "Cannot find module '@angular/material/progress-spinner' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 818, "length": 29, "messageText": "Cannot find module '@angular/material/snack-bar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 881, "length": 26, "messageText": "Cannot find module '@angular/material/dialog' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 942, "length": 27, "messageText": "Cannot find module '@angular/material/tooltip' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1004, "length": 27, "messageText": "Cannot find module '@angular/material/divider' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1069, "length": 30, "messageText": "Cannot find module '@angular/material/datepicker' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1137, "length": 24, "messageText": "Cannot find module '@angular/material/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1478, "length": 539, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 4271, "length": 6, "messageText": "Parameter 'church' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 4411, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5508, "length": 7, "code": 2339, "category": 1, "messageText": "Property 'forEach' does not exist on type '{}'."}, {"start": 5516, "length": 11, "messageText": "Parameter 'serviceTime' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 5690, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'length' does not exist on type '{}'."}, {"start": 6775, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 6827, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 6968, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 6993, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 7187, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 7209, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 7632, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 7685, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 7748, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 7809, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 8129, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8285, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 8721, "length": 6, "messageText": "Cannot find name 'Number'.", "category": 1, "code": 2304}, {"start": 8770, "length": 6, "messageText": "Cannot find name 'Number'.", "category": 1, "code": 2304}, {"start": 8825, "length": 6, "messageText": "Cannot find name 'Number'.", "category": 1, "code": 2304}, {"start": 8876, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 8930, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 9079, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 9104, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 9294, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 9316, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 9737, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 9790, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 9851, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 9910, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 10261, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10542, "length": 18, "messageText": "Parameter 'notificationResult' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10581, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 10794, "length": 17, "messageText": "Parameter 'notificationError' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10832, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 11091, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 12714, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'find' does not exist on type '{}'."}, {"start": 12719, "length": 6, "messageText": "Parameter 'option' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 48, [49, [{"start": 42, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 88, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 179, "length": 16, "messageText": "Cannot find module '@angular/forms' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 227, "length": 24, "messageText": "Cannot find module '@angular/material/card' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 285, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 343, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 400, "length": 25, "messageText": "Cannot find module '@angular/material/input' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 462, "length": 30, "messageText": "Cannot find module '@angular/material/form-field' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 526, "length": 26, "messageText": "Cannot find module '@angular/material/select' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 595, "length": 36, "messageText": "Cannot find module '@angular/material/progress-spinner' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 680, "length": 29, "messageText": "Cannot find module '@angular/material/snack-bar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 754, "length": 26, "messageText": "Cannot find module '@angular/material/dialog' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 815, "length": 27, "messageText": "Cannot find module '@angular/material/tooltip' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 877, "length": 27, "messageText": "Cannot find module '@angular/material/divider' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 937, "length": 25, "messageText": "Cannot find module '@angular/material/chips' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 995, "length": 25, "messageText": "Cannot find module '@angular/material/badge' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1052, "length": 24, "messageText": "Cannot find module '@angular/material/tabs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 1216, "length": 8149, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 10014, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 10063, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 10124, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 10185, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 10449, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 10773, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 10798, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 11258, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 11280, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 11337, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 12064, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 12104, "length": 6, "messageText": "Cannot find name 'Object'.", "category": 1, "code": 2304}, {"start": 12156, "length": 3, "messageText": "Parameter 'key' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 12272, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 12561, "length": 8, "messageText": "Parameter 'response' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 12655, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 12675, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 13686, "length": 6, "code": 2339, "category": 1, "messageText": "Property 'charAt' does not exist on type 'NotificationPriority'."}, {"start": 13721, "length": 5, "code": 2339, "category": 1, "messageText": "Property 'slice' does not exist on type 'NotificationPriority'."}, {"start": 13755, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}, {"start": 13786, "length": 4, "messageText": "Cannot find name 'Date'.", "category": 1, "code": 2304}]], [50, [{"file": false, "messageText": "Cannot find global type 'Promise'.", "category": 1, "code": 2318}, {"start": 23, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 212, "length": 46, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 264, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 399, "length": 58, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 463, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 597, "length": 60, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 663, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 866, "length": 76, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 948, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1078, "length": 62, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 1146, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1290, "length": 50, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 1346, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1528, "length": 73, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 1607, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1786, "length": 46, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 1838, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2010, "length": 46, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 2062, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2235, "length": 48, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 2289, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 2468, "length": 58, "messageText": "A dynamic import call returns a 'Promise'. Make sure you have a declaration for 'Promise' or include 'ES2015' in your '--lib' option.", "category": 1, "code": 2711}, {"start": 2532, "length": 1, "messageText": "Parameter 'm' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 51, [52, [{"start": 62, "length": 22, "messageText": "Cannot find module '@angular/common/http' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 109, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 176, "length": 6, "messageText": "Cannot find module 'rxjs' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 583, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'some' does not exist on type '{}'."}, {"start": 588, "length": 8, "messageText": "Parameter 'endpoint' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 925, "length": 5, "messageText": "Parameter 'error' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 1474, "length": 12, "messageText": "Parameter 'refreshError' implicitly has an 'any' type.", "category": 1, "code": 7006}]], 53, 54, [55, [{"start": 34, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 81, "length": 17, "messageText": "Cannot find module '@angular/router' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 103, "length": 125, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}]], [56, [{"start": 37, "length": 27, "messageText": "Cannot find module '@angular/platform-browser' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 198, "length": 3, "messageText": "Parameter 'err' implicitly has an 'any' type.", "category": 1, "code": 7006}, {"start": 206, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}]], 57, 58, [61, [{"start": 105, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 151, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 198, "length": 16, "messageText": "Cannot find module '@angular/forms' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 251, "length": 30, "messageText": "Cannot find module '@angular/material/form-field' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 314, "length": 25, "messageText": "Cannot find module '@angular/material/input' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 373, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 431, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 498, "length": 36, "messageText": "Cannot find module '@angular/material/progress-spinner' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 583, "length": 29, "messageText": "Cannot find module '@angular/material/snack-bar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 748, "length": 357, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 3336, "length": 7, "messageText": "Cannot find name 'Promise'. Do you need to change your target library? Try changing the 'lib' compiler option to 'es2015' or later.", "category": 1, "code": 2583}, {"start": 3380, "length": 4, "code": 2339, "category": 1, "messageText": "Property 'trim' does not exist on type 'string'."}, {"start": 3645, "length": 5, "messageText": "Cannot find name 'fetch'.", "category": 1, "code": 2304}, {"start": 3720, "length": 18, "messageText": "Cannot find name 'encodeURIComponent'.", "category": 1, "code": 2304}, {"start": 3971, "length": 10, "messageText": "Cannot find name 'parseFloat'.", "category": 1, "code": 2304}, {"start": 4016, "length": 10, "messageText": "Cannot find name 'parseFloat'.", "category": 1, "code": 2304}, {"start": 4410, "length": 7, "messageText": "Cannot find name 'console'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}]], 62, [63, [{"start": 63, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 109, "length": 17, "messageText": "Cannot find module '@angular/common' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 160, "length": 26, "messageText": "Cannot find module '@angular/material/button' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 218, "length": 24, "messageText": "Cannot find module '@angular/material/icon' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 285, "length": 36, "messageText": "Cannot find module '@angular/material/progress-spinner' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 370, "length": 29, "messageText": "Cannot find module '@angular/material/snack-bar' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 431, "length": 24, "messageText": "Cannot find module '@angular/material/card' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 458, "length": 303, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}, {"start": 890, "length": 4, "messageText": "Cannot find name '<PERSON>'.", "category": 1, "code": 2304}, {"start": 1144, "length": 8, "messageText": "Cannot find name 'document'. Do you need to change your target library? Try changing the 'lib' compiler option to include 'dom'.", "category": 1, "code": 2584}, {"start": 1192, "length": 16, "messageText": "Cannot find name 'HTMLInputElement'.", "category": 1, "code": 2304}, {"start": 1263, "length": 5, "messageText": "Cannot find name 'Event'.", "category": 1, "code": 2304}, {"start": 1312, "length": 16, "messageText": "Cannot find name 'HTMLInputElement'.", "category": 1, "code": 2304}, {"start": 1518, "length": 8, "code": 2339, "category": 1, "messageText": "Property 'includes' does not exist on type '{}'."}]], [64, [{"start": 25, "length": 15, "messageText": "Cannot find module '@angular/core' or its corresponding type declarations.", "category": 1, "code": 2307}, {"start": 205, "length": 155, "messageText": "This syntax requires an imported helper but module 'tslib' cannot be found.", "category": 1, "code": 2354}]], 65], "version": "5.8.3"}