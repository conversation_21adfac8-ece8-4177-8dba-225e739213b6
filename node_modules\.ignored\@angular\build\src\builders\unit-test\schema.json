{"$schema": "http://json-schema.org/draft-07/schema", "title": "Unit testing", "description": "Unit testing options for Angular applications.", "type": "object", "properties": {"buildTarget": {"type": "string", "description": "A build builder target to serve in the format of `project:target[:configuration]`. You can also pass in more than one configuration name as a comma-separated list. Example: `project:target:production,staging`.", "pattern": "^[^:\\s]*:[^:\\s]*(:[^\\s]+)?$"}, "tsConfig": {"type": "string", "description": "The name of the TypeScript configuration file."}, "runner": {"type": "string", "description": "The name of the test runner to use for test execution.", "enum": ["karma", "vitest"]}, "browsers": {"description": "A list of browsers to use for test execution. If undefined, jsdom on Node.js will be used instead of a browser.", "type": "array", "items": {"type": "string"}, "minItems": 1}, "include": {"type": "array", "items": {"type": "string"}, "default": ["**/*.spec.ts"], "description": "Globs of files to include, relative to project root. \nThere are 2 special cases:\n - when a path to directory is provided, all spec files ending \".spec.@(ts|tsx)\" will be included\n - when a path to a file is provided, and a matching spec file exists it will be included instead."}, "exclude": {"type": "array", "items": {"type": "string"}, "default": [], "description": "Globs of files to exclude, relative to the project root."}, "watch": {"type": "boolean", "description": "Re-run tests when source files change. Defaults to `true` in TTY environments and `false` otherwise."}, "debug": {"type": "boolean", "description": "Initialize the test runner to support using the Node Inspector for test debugging.", "default": false}, "codeCoverage": {"type": "boolean", "description": "Output a code coverage report.", "default": false}, "codeCoverageExclude": {"type": "array", "description": "Globs to exclude from code coverage.", "items": {"type": "string"}, "default": []}, "codeCoverageReporters": {"type": "array", "description": "Reporters to use for code coverage results.", "items": {"oneOf": [{"$ref": "#/definitions/coverage-reporters"}, {"type": "array", "minItems": 1, "maxItems": 2, "items": [{"$ref": "#/definitions/coverage-reporters"}, {"type": "object"}]}]}}, "reporters": {"type": "array", "description": "Test runner reporters to use. Directly passed to the test runner.", "items": {"type": "string"}}, "providersFile": {"type": "string", "description": "TypeScript file that exports an array of Angular providers to use during test execution. The array must be a default export.", "minLength": 1}, "setupFiles": {"type": "array", "items": {"type": "string"}, "description": "A list of global setup and configuration files that are included before the test files. The application's polyfills are always included before these files. The Angular Testbed is also initialized prior to the execution of these files."}}, "additionalProperties": false, "required": ["buildTarget", "tsConfig", "runner"], "definitions": {"coverage-reporters": {"enum": ["html", "lcov", "lcovonly", "text", "text-summary", "cobertura", "json", "json-summary"]}}}