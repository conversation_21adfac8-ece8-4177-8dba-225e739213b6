A workspace has a single CLI configuration file, `angular.json`, at the top level.
The `projects` object contains a configuration object for each project in the workspace.

You can edit the configuration directly in a code editor,
or indirectly on the command line using this command.

The configurable property names match command option names,
except that in the configuration file, all names must use camelCase,
while on the command line options can be given dash-case.

For further details, see [Workspace Configuration](reference/configs/workspace-config).

For configuration of CLI usage analytics, see [ng analytics](cli/analytics).
