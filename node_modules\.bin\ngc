#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/bundles/src/bin/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/bundles/src/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/bundles/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/bundles/src/bin/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/bundles/src/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/bundles/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../@angular/compiler-cli/bundles/src/bin/ngc.js" "$@"
else
  exec node  "$basedir/../@angular/compiler-cli/bundles/src/bin/ngc.js" "$@"
fi
