{"version": 3, "sources": ["../../../../../../node_modules/@angular/material/fesm2022/select.mjs"], "sourcesContent": ["export { a as MatOptgroup, M as MatOption } from './option-BzhYL_xC.mjs';\nexport { b as <PERSON><PERSON><PERSON><PERSON>, j as <PERSON><PERSON><PERSON><PERSON><PERSON>, c as <PERSON><PERSON><PERSON>, M as <PERSON><PERSON><PERSON><PERSON>, e as <PERSON><PERSON><PERSON><PERSON>x, g as <PERSON><PERSON><PERSON><PERSON> } from './form-field-CFbrnFED.mjs';\nexport { c as MAT_SELECT_CONFIG, a as MAT_SELECT_SCROLL_STRATEGY, d as MAT_SELECT_SCROLL_STRATEGY_PROVIDER, b as MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, e as MAT_SELECT_TRIGGER, g as MatSelect, f as MatSelectChange, M as MatSelectModule, h as MatSelectTrigger } from './module-B0CLRw5e.mjs';\nimport '@angular/cdk/a11y';\nimport '@angular/cdk/keycodes';\nimport '@angular/core';\nimport 'rxjs';\nimport './ripple-BYgV4oZC.mjs';\nimport '@angular/cdk/platform';\nimport '@angular/cdk/coercion';\nimport '@angular/cdk/private';\nimport './animation-DfMFjxHu.mjs';\nimport '@angular/cdk/layout';\nimport './pseudo-checkbox-DDmgx3P4.mjs';\nimport './structural-styles-CObeNzjn.mjs';\nimport '@angular/cdk/bidi';\nimport '@angular/common';\nimport 'rxjs/operators';\nimport '@angular/cdk/observers/private';\nimport '@angular/cdk/overlay';\nimport '@angular/cdk/scrolling';\nimport '@angular/cdk/collections';\nimport '@angular/forms';\nimport './error-options-DCNQlTOA.mjs';\nimport './error-state-Dtb1IHM-.mjs';\nimport './index-DwiL-HGk.mjs';\nimport './index-BFRo2fUq.mjs';\nimport './common-module-cKSwHniA.mjs';\nimport './pseudo-checkbox-module-4F8Up4PL.mjs';\nimport './module-B62K-792.mjs';\nimport '@angular/cdk/observers';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n * @deprecated No longer used, will be removed.\n * @breaking-change 21.0.0\n */\nconst matSelectAnimations = {\n    // Represents\n    // trigger('transformPanel', [\n    //   state(\n    //     'void',\n    //     style({\n    //       opacity: 0,\n    //       transform: 'scale(1, 0.8)',\n    //     }),\n    //   ),\n    //   transition(\n    //     'void => showing',\n    //     animate(\n    //       '120ms cubic-bezier(0, 0, 0.2, 1)',\n    //       style({\n    //         opacity: 1,\n    //         transform: 'scale(1, 1)',\n    //       }),\n    //     ),\n    //   ),\n    //   transition('* => void', animate('100ms linear', style({opacity: 0}))),\n    // ])\n    /** This animation transforms the select's overlay panel on and off the page. */\n    transformPanel: {\n        type: 7,\n        name: 'transformPanel',\n        definitions: [\n            {\n                type: 0,\n                name: 'void',\n                styles: {\n                    type: 6,\n                    styles: { opacity: 0, transform: 'scale(1, 0.8)' },\n                    offset: null,\n                },\n            },\n            {\n                type: 1,\n                expr: 'void => showing',\n                animation: {\n                    type: 4,\n                    styles: {\n                        type: 6,\n                        styles: { opacity: 1, transform: 'scale(1, 1)' },\n                        offset: null,\n                    },\n                    timings: '120ms cubic-bezier(0, 0, 0.2, 1)',\n                },\n                options: null,\n            },\n            {\n                type: 1,\n                expr: '* => void',\n                animation: {\n                    type: 4,\n                    styles: { type: 6, styles: { opacity: 0 }, offset: null },\n                    timings: '100ms linear',\n                },\n                options: null,\n            },\n        ],\n        options: {},\n    },\n};\n\nexport { matSelectAnimations };\n\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCA,IAAM,sBAAsB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAuBxB,gBAAgB;AAAA,IACZ,MAAM;AAAA,IACN,MAAM;AAAA,IACN,aAAa;AAAA,MACT;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,QAAQ;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ,EAAE,SAAS,GAAG,WAAW,gBAAgB;AAAA,UACjD,QAAQ;AAAA,QACZ;AAAA,MACJ;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW;AAAA,UACP,MAAM;AAAA,UACN,QAAQ;AAAA,YACJ,MAAM;AAAA,YACN,QAAQ,EAAE,SAAS,GAAG,WAAW,cAAc;AAAA,YAC/C,QAAQ;AAAA,UACZ;AAAA,UACA,SAAS;AAAA,QACb;AAAA,QACA,SAAS;AAAA,MACb;AAAA,MACA;AAAA,QACI,MAAM;AAAA,QACN,MAAM;AAAA,QACN,WAAW;AAAA,UACP,MAAM;AAAA,UACN,QAAQ,EAAE,MAAM,GAAG,QAAQ,EAAE,SAAS,EAAE,GAAG,QAAQ,KAAK;AAAA,UACxD,SAAS;AAAA,QACb;AAAA,QACA,SAAS;AAAA,MACb;AAAA,IACJ;AAAA,IACA,SAAS,CAAC;AAAA,EACd;AACJ;", "names": []}