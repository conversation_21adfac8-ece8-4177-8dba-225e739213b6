#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\dev\flockin\frontend\node_modules\.pnpm\@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334\node_modules\@angular\compiler-cli\bundles\src\bin\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334\node_modules\@angular\compiler-cli\bundles\src\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334\node_modules\@angular\compiler-cli\bundles\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334\node_modules\@angular\compiler-cli\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334\node_modules\@angular\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334\node_modules;C:\dev\flockin\frontend\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/bundles/src/bin/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/bundles/src/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/bundles/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/compiler-cli/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules/@angular/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/@angular+compiler-cli@20.1._a54aecca7c281ba9406bb2872226e334/node_modules:/mnt/c/dev/flockin/frontend/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../@angular/compiler-cli/bundles/src/bin/ngc.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../@angular/compiler-cli/bundles/src/bin/ngc.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../@angular/compiler-cli/bundles/src/bin/ngc.js" $args
  } else {
    & "node$exe"  "$basedir/../@angular/compiler-cli/bundles/src/bin/ngc.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
