import {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger
} from "./chunk-EN62HSQJ.js";
import "./chunk-XGA4RVUQ.js";
import "./chunk-6JZJJECR.js";
import {
  MatOptgroup,
  MatOption
} from "./chunk-YINS7MVG.js";
import "./chunk-XANWJQBZ.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>F<PERSON>,
  Mat<PERSON><PERSON>,
  MatLabel,
  MatPrefix,
  MatSuffix
} from "./chunk-ZR36I3TN.js";
import "./chunk-QDCBAJSJ.js";
import "./chunk-KUQ4HJ25.js";
import "./chunk-FHZGUNJH.js";
import "./chunk-WOSGJU5E.js";
import "./chunk-LS7RHOOU.js";
import "./chunk-J7FBKCQR.js";
import "./chunk-OUAKEVN2.js";
import "./chunk-U2OHI465.js";
import "./chunk-H22FBQC4.js";
import "./chunk-QCETVJKM.js";
import "./chunk-DQ7OVFPD.js";
import "./chunk-OQCX5PK4.js";
import "./chunk-6OT5U7L4.js";
import "./chunk-EOFW2REK.js";
import "./chunk-YLJ7OMXF.js";
import "./chunk-RY3UWGVD.js";
import "./chunk-IVJXOQLC.js";
import "./chunk-S3DA5VOB.js";
import "./chunk-J2JSG7JR.js";
import "./chunk-CVFHTG5G.js";
import "./chunk-7DPHYZ4E.js";
import "./chunk-4RUIP7CR.js";
import "./chunk-YVXMBCE5.js";
import "./chunk-G6ECYYJH.js";
import "./chunk-RTGP7ALM.js";
import "./chunk-46DXP6YY.js";

// node_modules/@angular/material/fesm2022/select.mjs
var matSelectAnimations = {
  // Represents
  // trigger('transformPanel', [
  //   state(
  //     'void',
  //     style({
  //       opacity: 0,
  //       transform: 'scale(1, 0.8)',
  //     }),
  //   ),
  //   transition(
  //     'void => showing',
  //     animate(
  //       '120ms cubic-bezier(0, 0, 0.2, 1)',
  //       style({
  //         opacity: 1,
  //         transform: 'scale(1, 1)',
  //       }),
  //     ),
  //   ),
  //   transition('* => void', animate('100ms linear', style({opacity: 0}))),
  // ])
  /** This animation transforms the select's overlay panel on and off the page. */
  transformPanel: {
    type: 7,
    name: "transformPanel",
    definitions: [
      {
        type: 0,
        name: "void",
        styles: {
          type: 6,
          styles: { opacity: 0, transform: "scale(1, 0.8)" },
          offset: null
        }
      },
      {
        type: 1,
        expr: "void => showing",
        animation: {
          type: 4,
          styles: {
            type: 6,
            styles: { opacity: 1, transform: "scale(1, 1)" },
            offset: null
          },
          timings: "120ms cubic-bezier(0, 0, 0.2, 1)"
        },
        options: null
      },
      {
        type: 1,
        expr: "* => void",
        animation: {
          type: 4,
          styles: { type: 6, styles: { opacity: 0 }, offset: null },
          timings: "100ms linear"
        },
        options: null
      }
    ],
    options: {}
  }
};
export {
  MAT_SELECT_CONFIG,
  MAT_SELECT_SCROLL_STRATEGY,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER,
  MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,
  MAT_SELECT_TRIGGER,
  MatError,
  MatFormField,
  MatHint,
  MatLabel,
  MatOptgroup,
  MatOption,
  MatPrefix,
  MatSelect,
  MatSelectChange,
  MatSelectModule,
  MatSelectTrigger,
  MatSuffix,
  matSelectAnimations
};
//# sourceMappingURL=@angular_material_select.js.map
